ModernAction.io: Enhanced MVP Implementation Plan (v2.1)
Version: 2.1
Lead: Lead Technical Advisor
Timeline: 6 Months
Related Document: ModernAction.io - The Master Blueprint (Internal Link)

A Note to Our Developers: The ModernAction.io Way
This document is your guide. It is intentionally verbose and prescriptive to ensure clarity and minimize ambiguity. Your primary directive is to build with quality and document your work.

Documentation is Not Optional: After completing each step, you must update our internal project documentation (e.g., in Confluence or Notion). Explain the "why" behind your implementation choices. If you deviate from this plan for a good reason, document it.

Keep a Log: At the end of each step, there is a Developer Log section. You are encouraged to add brief, dated notes here about your progress, challenges, or decisions. This makes this a living document.

Testing is Paramount: After each logical feature is complete, you must write and run end-to-end tests. We are using a test-driven mindset. If a test fails, do not proceed.

What to do if a test fails:

STOP: Do not merge the Pull Request.

DOCUMENT: In the PR comments and our issue tracker, document the failure with screenshots, logs, and steps to reproduce.

ITERATE: On your feature branch, work through the bug. Document your debugging process and the final fix in your commit messages.

RE-TEST: Re-run all relevant unit, integration, and end-to-end tests until they pass.

MERGE: Once all tests are green, you may request a final review for merging.

Let's build the best damn civic action platform in the world.


Phase 1: Foundation & Core Services (Months 1-2)

Goal: Establish the project's technical foundation, set up the development environment, and build the core data models and services.

Sprint 1: Project Setup & AWS Foundation (Weeks 1-2)

Step 1-2: Initialize Monorepo & Frontend

Analysis: No issues. The original steps are correct.

Developer Log: (Required after completion)

Step 3: Initialize FastAPI Backend

Analysis: The initial setup can be more robust to promote a scalable structure.

Recommended Enhancement:

Navigate to apps/api. Initialize Poetry and add fastapi, uvicorn, pydantic, and python-dotenv.

Create a more organized project structure within apps/api:

Generated code
apps/api/
├── app/
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   └── v1/
│   │       ├── __init__.py
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           └── health.py
│   ├── core/
│   │   └── config.py
│   └── main.py
├── poetry.lock
└── pyproject.toml


In app/core/config.py, use Pydantic's BaseSettings to manage environment variables.

Generated python
# app/core/config.py
from pydantic import BaseSettings

class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    # Add other settings here later

    class Config:
        case_sensitive = True

settings = Settings()
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

In app/api/v1/endpoints/health.py, create the health check router.

Generated python
# app/api/v1/endpoints/health.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/health", status_code=200)
def health_check():
    return {"status": "ok"}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Tie it together in app/main.py.

Generated python
# app/main.py
from fastapi import FastAPI
from app.api.v1.endpoints import health

app = FastAPI(title="ModernAction API")

app.include_router(health.router, prefix="/api/v1", tags=["health"])
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Documentation:

FastAPI Bigger Applications - Multiple Files

Pydantic Settings Management

Developer Log:

Step 4-7: AWS Setup, RDS, Secrets & CI/CD

Analysis: These steps are solid. I will add a crucial sub-step for local development ease.

Recommended Enhancement (add to Step 6):

In the apps/api directory, create a .env.example file. This file will list all required environment variables (like DATABASE_URL) but with placeholder values. It should not contain secrets and must be committed to Git. This instructs other developers on what variables they need to set up in their own .env file (which is in .gitignore).

Developer Log:

Step 8: Initial Feature & Testing Mandate

Analysis: The mandate needs more specific instructions.

Recommended Enhancement:

Backend Test (apps/api/tests/test_health.py):

Generated python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_health_check():
    response = client.get("/api/v1/health")
    assert response.status_code == 200
    assert response.json() == {"status": "ok"}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Frontend Test (apps/web/components/HomePage.test.tsx):

Generated typescript
import { render, screen } from '@testing-library/react';
import Home from '../pages/index';

test('renders a heading', () => {
  render(<Home />);
  const heading = screen.getByRole('heading', {
    name: /welcome to next\.js/i,
  });
  expect(heading).toBeInTheDocument();
});
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END

E2E Test Setup (Playwright): Initialize Playwright in the root directory. Add a simple test to visit the homepage. Configure the CI/CD pipeline to install browsers and run this test.

Generated javascript
// e2e/example.spec.ts
import { test, expect } from '@playwright/test';

test('homepage has correct title', async ({ page }) => {
  await page.goto('http://localhost:3000');
  await expect(page).toHaveTitle(/Create Next App/);
});
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
JavaScript
IGNORE_WHEN_COPYING_END

Documentation:

FastAPI Testing

React Testing Library with Next.js

Playwright Installation

Developer Log:

Sprint 2: Database Modeling & Backend Setup (Weeks 3-4)

Step 9-10: ORM & Core Database Models

Analysis: This is a high-risk step. Defining models correctly is critical. We can enforce best practices from the start.

Recommended Enhancement:

Create a base model in app/db/base_class.py that all other models will inherit. This ensures consistency with primary keys and timestamps.

Generated python
# app/db/base_class.py
from sqlalchemy.ext.declarative import as_declarative, declared_attr
import uuid
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import Column, DateTime, func

@as_declarative()
class Base:
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    created_at = Column(DateTime, server_default=func.now(), nullable=False)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), nullable=False)
    __name__: str
    # Generate table name automatically
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower() + "s"
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

When defining models (app/models/user.py, app/models/bill.py, etc.), inherit from this Base class. This provides id, created_at, updated_at automatically.

Define relationships explicitly with back_populates to prevent ambiguity.

Generated python
# app/models/campaign.py
from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class Campaign(Base):
    title = Column(String, nullable=False)
    bill_id = Column(UUID(as_uuid=True), ForeignKey("bills.id"))
    bill = relationship("Bill", back_populates="campaigns")
    actions = relationship("Action", back_populates="campaign")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Documentation:

SQLAlchemy Relationship Configuration

Developer Log:

Step 11-12: Migrations & Testing Mandate

Analysis: Good instructions. The testing can be more specific.

Recommended Enhancement (Testing Mandate):

For each model, write a unit test that creates an instance of the model, adds it to a test session, commits, and then queries it back to verify data integrity. This validates default values, relationships, and constraints. Use an in-memory SQLite database for speed if possible, or a dedicated local test Postgres DB.

Developer Log:

Sprint 3: Seeding the Database & API Foundation (Weeks 5-6)

Step 13-19: External APIs, Ingestion & Endpoints

Analysis: This section is ripe for adding detail on data validation and API structure.

Recommended Enhancement (for each new endpoint):

Define Pydantic Schemas: Before writing the endpoint, define the Pydantic schemas for the API response in a new app/schemas/ directory. This is your API's contract.

Generated python
# app/schemas/official.py
from pydantic import BaseModel, EmailStr

class Official(BaseModel):
    name: str
    party: str
    email: EmailStr

    class Config:
        orm_mode = True # This allows the model to be created from an ORM object
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Use Schemas in Endpoint: Use the schema in your endpoint definition with response_model. FastAPI will automatically handle data serialization and documentation.

Generated python
# app/api/v1/endpoints/officials.py
from fastapi import APIRouter
from typing import List
from app import schemas

router = APIRouter()

@router.get("/officials", response_model=List[schemas.Official])
def get_officials(zip_code: str):
    # ... service logic to fetch officials ...
    # The return value will be validated against List[schemas.Official]
    return officials_from_db
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Documentation:

FastAPI Response Model

Developer Log:

Phase 2: Core Action Loop & AI Integration (Months 3-4)
Sprint 5: AI Integration - Bill Summarization (Weeks 9-10)

Step 28: Develop Bill Summarization Service

Analysis: This is a major performance bottleneck if implemented naively. The model must be loaded only once.

Recommended Enhancement:

Create a singleton or a cached dependency to hold the model pipeline.

Generated python
# app/core/ml.py
from functools import lru_cache
from transformers import pipeline

@lru_cache(maxsize=None) # A simple way to create a singleton
def get_summarizer():
    # This will only run once
    print("Loading summarization model...")
    return pipeline("summarization", model="t5-small")
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

In your service, get the model via this function.

Generated python
# app/services/ai.py
from app.core.ml import get_summarizer

def summarize_text(text: str) -> str:
    summarizer = get_summarizer()
    # ... logic to handle summarization ...
    summary = summarizer(text, max_length=150, min_length=30, do_sample=False)
    return summary[0]['summary_text']
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
Python
IGNORE_WHEN_COPYING_END

Documentation:

FastAPI Dependencies with yield (A more robust way to manage resources like model lifecycles)

Hugging Face Summarization Task

Developer Log:

Sprint 6: The Action Modal & Email Integration (Weeks 11-12)

Step 33: Implement POST /actions Endpoint Logic

Analysis: This is a critical write-operation. It needs robust validation and clear separation of concerns.

Recommended Enhancement:

Define a Pydantic schema for the incoming request body in app/schemas/action.py.

In the endpoint, the first step is to validate this input.

The endpoint's only job should be: 1) Validate input, 2) Create the Action record in the DB, 3) Dispatch the background task with the new Action's ID.

All other logic (fetching official's email, calling SES) must happen inside the background task to keep the API response fast.

Developer Log:

Step 35: Configure AWS SES

Analysis: This is good. Add a security note.

Recommended Enhancement:

Security: Create a new, dedicated IAM user with a policy that only allows access to ses:SendEmail and nothing else. The backend will use this user's credentials. This follows the principle of least privilege.

Documentation:

Amazon SES IAM Policies

Developer Log:

Phase 3: Closing the Loop & Launch Prep (Months 5-6)
Sprint 9: Deployment & Infrastructure as Code (Weeks 17-18)

Step 50: Define Infrastructure as Code (IaC)

Analysis: This is a fantastic step. It needs to be tied back to the project configuration.

Recommended Enhancement:

The AWS CDK script should be responsible for creating the Secrets Manager secrets.

The script should then pass the ARNs of these secrets into the Fargate Task Definition's secrets configuration. This ensures the application containers get their database credentials securely without hardcoding.

Documentation:

Injecting secrets into Fargate Tasks via AWS Secrets Manager

Developer Log:

Sprint 10: Final Testing & Launch (Weeks 19-24)

Step 53: Full End-to-End Regression Testing

Analysis: This step should be more structured.

Recommended Enhancement:

Create a test plan document in the wiki that maps every user story from the PRD to a specific Playwright test file.

Before this sprint, ensure all interactive elements in the React app have a data-cy or data-testid attribute. This makes E2E tests far more resilient to CSS changes.

The E2E tests should run against the live staging environment and use a dedicated test user account. The test suite should clean up any data it creates.

Developer Log:

Step 54: Load Testing

Analysis: Needs more context on what to measure.

Recommended Enhancement:

Define success criteria before the test. For example: "Average response time for GET /campaigns/{id} must be under 200ms" and "POST /actions must be under 100ms."

The load test script should simulate a realistic user flow: 80% of users browse campaign pages, 20% complete the action flow.

Monitor the RDS instance's CPU utilization and the Fargate tasks' CPU/memory during the test to identify the bottleneck.

Documentation:

k6 Scenarios

Developer Log:

This enhanced plan provides the level of detail necessary for developers to build with confidence, quality, and speed. It front-loads architectural decisions, embeds best practices for security and performance, and creates a clear, testable path to a successful MVP launch.